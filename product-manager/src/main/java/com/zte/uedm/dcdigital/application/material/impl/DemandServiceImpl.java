package com.zte.uedm.dcdigital.application.material.impl;
/* Started by AICoder, pid:0b155947a9n99a314fb00a1f70528630a8b55413 */

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.base.CaseFormat;
import com.zte.uedm.dcdigital.application.category.ProductCategoryQueryService;
import com.zte.uedm.dcdigital.application.material.DemandService;
import com.zte.uedm.dcdigital.application.material.MaterialMaintenanceCommandService;
import com.zte.uedm.dcdigital.application.material.MaterialMaintenanceQueryService;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.bean.brand.BusinessLectotypeStatisticVo;
import com.zte.uedm.dcdigital.common.bean.document.FileInfoVo;
import com.zte.uedm.dcdigital.common.bean.enums.IdNameBean;
import com.zte.uedm.dcdigital.common.bean.enums.system.PermissionEnum;
import com.zte.uedm.dcdigital.common.bean.enums.system.RoleCodeEnum;
import com.zte.uedm.dcdigital.common.bean.product.DemandManagementInnerDto;
import com.zte.uedm.dcdigital.common.bean.product.DemandManagementInnerVo;
import com.zte.uedm.dcdigital.common.bean.product.DemandManagementLectotypeUpdDto;
import com.zte.uedm.dcdigital.common.bean.product.DemandProcDto;
import com.zte.uedm.dcdigital.common.bean.project.ProjectDetailInfoVo;
import com.zte.uedm.dcdigital.common.bean.system.RoleVo;
import com.zte.uedm.dcdigital.common.bean.system.UserVo;
import com.zte.uedm.dcdigital.common.exception.BusinessException;
import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.common.util.RpcUtil;
import com.zte.uedm.dcdigital.common.web.StatusCode;
import com.zte.uedm.dcdigital.domain.common.enums.*;
import com.zte.uedm.dcdigital.domain.common.statuscode.ProductCategoryStatusCode;
import com.zte.uedm.dcdigital.domain.service.ProcurementDomainService;
import com.zte.uedm.dcdigital.domain.service.ProductCategoryDomainService;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.DemandMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.DemandManagementPo;
import com.zte.uedm.dcdigital.interfaces.web.material.dto.*;
import com.zte.uedm.dcdigital.interfaces.web.material.vo.*;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ManagerVo;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductCategoryManagerVo;
import com.zte.uedm.dcdigital.interfaces.web.product.vo.ProductCategoryVo;
import com.zte.uedm.dcdigital.sdk.document.service.DocumentService;
import com.zte.uedm.dcdigital.sdk.process.rpc.DemandProcessRpc;
import com.zte.uedm.dcdigital.sdk.project.dto.BillOfQuantityQueryDto;
import com.zte.uedm.dcdigital.sdk.project.rpc.BillQuantityRpc;
import com.zte.uedm.dcdigital.sdk.project.rpc.ProjectRpc;
import com.zte.uedm.dcdigital.sdk.project.vo.BillOfQuantityVo;
import com.zte.uedm.dcdigital.sdk.system.dto.MsgLogDto;
import com.zte.uedm.dcdigital.sdk.system.rpc.MsgRpc;
import com.zte.uedm.dcdigital.sdk.system.rpc.SystemRpc;
import com.zte.uedm.dcdigital.sdk.system.service.AuthService;
import com.zte.uedm.dcdigital.security.util.PermissionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

/**
 * DemandServiceImpl 类实现了 DemandService 接口，提供需求管理的具体实现。
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DemandServiceImpl implements DemandService {

    @Autowired
    private AuthService authService; // 注入认证服务

    @Autowired
    private DemandMapper demandMapper; // 注入需求管理的数据库操作映射器

    @Autowired
    private MaterialMaintenanceCommandService materialMaintenanceCommandService;

    @Autowired
    private MsgRpc msgRpc;

    @Autowired
    private ProductCategoryDomainService productCategoryDomainService;

    @Autowired
    private BillQuantityRpc billQuantityRpc;

    @Autowired
    private DemandProcessRpc demandProcessRpc;

    @Autowired
    private SystemRpc systemRpc;

    @Autowired
    private ProjectRpc projectRpc;

    @Autowired
    private DocumentService documentService;

    @Autowired
    private ProcurementDomainService procurementDomainService;

    @Autowired
    private PermissionUtil permissionUtil;

    @Autowired
    private ProductCategoryQueryService productCategoryQueryService;

    @Autowired
    private MaterialMaintenanceQueryService materialMaintenanceQueryService;
    /**
     * 添加新的需求管理记录。
     *
     * @param demandManagementDto 包含需求管理信息的数据传输对象
     */
    @Override
    public void addDemand(DemandManagementDto demandManagementDto) {
        String id = UUID.randomUUID().toString(); // 生成唯一的ID
        demandManagementDto.setId(id); // 设置ID到数据传输对象
        demandMapper.addDemand(demandManagementDto); // 调用数据库操作映射器添加需求记录
    }

    @Override
    public PageVO<DemandManagementVo> pageDemand(PageDemandDto pageDemandDto) {
        if(StringUtils.isNotBlank(pageDemandDto.getProductCategoryId())){
            List<RoleVo> roleVos =RpcUtil.call(systemRpc.queryRoleByUserIdAndResourceId(authService.getUserId(),pageDemandDto.getProductCategoryId()));

            if(null!=roleVos&&roleVos.size()>0) {
                //产品经理（可看到、转化所负责的所有产品小类的需求）、产品SE（仅可看到、转化，处理人为自己的）
                List<String> roles = roleVos.stream().map(RoleVo::getCode).collect(Collectors.toList());
                log.info("roles:{}",JSON.toJSONString(roles));
                if(roles.contains(RoleCodeEnum.PRODUCT_SE.getCode())
                        &&!roles.contains(RoleCodeEnum.PRODUCT_MANAGER.getCode())
                        &&!roles.contains(RoleCodeEnum.COST_DIRECTOR.getCode())
                        &&!roles.contains(RoleCodeEnum.MATERIAL_ASSISTANT.getCode())){
                    pageDemandDto.setProcessor(authService.getUserId());
                }

            }else {
                pageDemandDto.setProcessor(authService.getUserId());
            }
        }

        Page<DemandManagementVo> page = PageHelper.startPage(pageDemandDto.getPage(), pageDemandDto.getSize());
        //驼峰转下划线
        pageDemandDto.setOrderField(CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, pageDemandDto.getOrderField()));
        List<DemandManagementVo> pageList = demandMapper.pageDemand(pageDemandDto);
        for (DemandManagementVo de:pageList ) {
            ProductCategoryVo result = productCategoryQueryService.queryProductCategoryById(de.getProductCategoryId());
            if(null!=result){
                de.setProductCategoryName(result.getProductName());
            }
            ProjectDetailInfoVo projectDetailInfoVoBaseResult = RpcUtil.call(projectRpc.selectProjectInfo(de.getProjectId()));
            if(null!=projectDetailInfoVoBaseResult){
                de.setProjectName(projectDetailInfoVoBaseResult.getName());
            }
            UserVo userVoBaseResult = RpcUtil.call(systemRpc.getUserInfoById(de.getProcessor()));
            if(null!=userVoBaseResult) {
                de.setProcessorName(userVoBaseResult.getName()+de.getProcessor());
            }
            UserVo userVoBaseResult1 = RpcUtil.call(systemRpc.getUserInfoById(de.getCreateUser()));
            if(null!=userVoBaseResult1) {
                de.setCreateUser(userVoBaseResult1.getName()+de.getCreateUser());
            }
        }

        return new PageVO<>(page.getTotal(), pageList);
    }

    @Override
    public PageVO<BillOfQuantityVo> pageQuantity(String demandId, Integer page,
                                                             Integer size) {
        DemandManagementPo demandManagementPo = demandMapper.selectDemandById(demandId);
        if (null != demandManagementPo) {
            BillOfQuantityQueryDto dto = new BillOfQuantityQueryDto();
            dto.setId(demandManagementPo.getProjectId());
            dto.setProductSubcategory(demandManagementPo.getProductCategoryId());
            PageVO<BillOfQuantityVo> baseResult = RpcUtil.call(billQuantityRpc.queryByProjectIdFilter(dto));
            return baseResult;
        }

        return null;
    }

    @Override
    public PageVO<DemandManagementLectotypeVo> pageLectotype(String demandId, Integer page, Integer size) {
        Page<DemandManagementLectotypeVo> voPage = PageHelper.startPage(page, size);
        List<DemandManagementLectotypeVo> pageList = demandMapper.pageLectotype(demandId);
        for (DemandManagementLectotypeVo de:pageList ) {
            UserVo userVoBaseResult = RpcUtil.call(systemRpc.getUserInfoById(de.getUpdateBy()));
            if(null!=userVoBaseResult) {
                de.setUpdateByName(userVoBaseResult.getName()+de.getUpdateBy());
            }
            UserVo userVoBaseResult2 = RpcUtil.call(systemRpc.getUserInfoById(de.getCreateBy()));
            if(null!=userVoBaseResult2) {
                de.setCreateByName(userVoBaseResult2.getName()+de.getCreateBy());
            }
            if(StringUtils.isNotBlank(de.getFileIds())) {
                List<String> ids = (List<String>) JSON.parse(de.getFileIds());
                List<FileInfoVo> documentInfoVos = documentService.queryByFileIds(ids);
                documentInfoVos.stream().forEach(fileInfoVo -> {fileInfoVo.setFileData(null);});
                de.setFileList(documentInfoVos);
            }else {
                de.setFileList(new ArrayList<>());
            }

        }
        return new PageVO<>(voPage.getTotal(), pageList);

    }

    @Override
    public DemandManagementPo detailDemand(String demandId) {
        DemandManagementPo demandManagementPo =  demandMapper.selectDemandById(demandId);

        UserVo userVoBaseResult = RpcUtil.call(systemRpc.getUserInfoById(demandManagementPo.getProcessor()));
        if(null!=userVoBaseResult) {
            demandManagementPo.setProcessorName(userVoBaseResult.getName()+demandManagementPo.getProcessor());
        }

        UserVo userVoBaseResult1 = RpcUtil.call(systemRpc.getUserInfoById(demandManagementPo.getCreateUser()));
        if(null!=userVoBaseResult1) {
            demandManagementPo.setCreateUser(userVoBaseResult1.getName()+demandManagementPo.getCreateUser());
            demandManagementPo.setCreateUserName(demandManagementPo.getCreateUser());
        }

        return demandManagementPo;
    }

    @Override
    public PageVO<MaterialDetailVo> detailDemandLectotypeMaterial(String lectotypeId, Integer page, Integer size) {

        Page<String> voPage = PageHelper.startPage(page, size);
        List<String> pageList = demandMapper.getLectotypeMaterialByLtc(lectotypeId);
        List<MaterialDetailVo> list = new ArrayList<>();
        if(pageList.size()>0){
            for (String s:pageList ) {
                MaterialDetailVo me = materialMaintenanceQueryService.queryMaterialDetailsById(s);
                log.debug("MaterialDetailVo:{}",me);
                list.add(me);
            }
        }


        return  new PageVO<>(voPage.getTotal(), list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addLectotype(DemandManagementLectotypeDto dto) {
        String letId = UUID.randomUUID().toString();
        dto.setLectotypeId(letId);
        dto.setCreateBy(authService.getUserId());
        dto.setCreateTime(DateTimeUtils.getCurrentTime());
        dto.setUpdateBy(authService.getUserId());
        dto.setUpdateTime(DateTimeUtils.getCurrentTime());
        dto.setFileIds(JSON.toJSONString(dto.getFileIdList()));
        if (dto.getLectotypeType().equals(LectotypeTypeEnums.ONE.type)) {
            dto.setLectotypeStatus(LectotypeStatusEnums.ONE.type);
        }else {
            dto.setLectotypeStatus(LectotypeStatusEnums.ZERO.type);
        }
        if(null!=dto.getFileIdList()&&dto.getFileIdList().size()>0){
            documentService.batchAddFiles(dto.getFileIdList());
        }
        demandMapper.addLectotype(dto);

        try {
            DemandManagementPo demandManagementPo = demandMapper.selectDemandById(dto.getDemandId());
            permissionUtil.checkPermission(demandManagementPo.getProductCategoryId(), PermissionEnum.PROCUREMENT_EDIT);
            ProcurementCostEditDto editDto = build(dto);
            editDto.parameterVerification();
            procurementDomainService.edit(editDto);
        }catch (Exception e){
            log.info("addLectotype:不是成本总监，没有修改权限！");
        }
        //需求转化可创建选型单，同时发送通知给成本总监 和 材料助理（选型单类型为招标时才通知材料助理），通知类型为：选型单
        DemandManagementPo demandManagementPo = demandMapper.selectDemandById(dto.getDemandId());
        if (null == demandManagementPo) {
            throw new BusinessException(ProductCategoryStatusCode.OBJECT_NULL_ERROR);
        }
        ProductCategoryManagerVo productCategoryManagerVo =
                productCategoryDomainService.queryProductCategoryManagerById(demandManagementPo.getProductCategoryId());
        if (null != productCategoryManagerVo) {
            List<ManagerVo> productSe = productCategoryManagerVo.getProductSe();
            DemandProcDto demandProcDto = new DemandProcDto();
            if (null != productSe && productSe.size() > 0) {
                List<String> productSes = productSe.stream().map(ManagerVo::getId).collect(Collectors.toList());
                demandProcDto.setCostDirector(productSes);
            }else {
                throw new BusinessException(ProductCategoryStatusCode.CATEGORY_NOT_CONFIG);
            }
            log.debug("getProductSe:{}",productSe);
            if (dto.getLectotypeType().equals(LectotypeTypeEnums.ONE.type)) {
                List<ManagerVo> materialAssistants = productCategoryManagerVo.getMaterialAssistants();
                log.debug("materialAssistants:{}",materialAssistants);
                if (null != materialAssistants && materialAssistants.size() > 0) {
                    log.debug("demandProcDto:{}",demandProcDto);
                    List<String> materialAssistantIds = materialAssistants.stream().map(ManagerVo::getId).collect(Collectors.toList());

                    if (null == materialAssistants || materialAssistantIds.size() == 0) {
                        throw new BusinessException(ProductCategoryStatusCode.CATEGORY_NOT_CONFIG);
                    }

                    demandProcDto.setMaterialAssistants(materialAssistantIds);
                    demandProcDto.setProductCategoryId(demandManagementPo.getProductCategoryId());
                    demandProcDto.setLectotypeId(letId);
                    demandProcDto.setLectotypeName(dto.getLectotypeName());
                    log.debug("demandProcDto:{}", JSON.toJSONString(demandProcDto));
                    RpcUtil.call(demandProcessRpc.addLectotypeProcess( demandProcDto));
                }else {
                    throw new BusinessException(ProductCategoryStatusCode.CATEGORY_NOT_CONFIG);
                }
            }
        } else {
            throw new BusinessException(ProductCategoryStatusCode.OBJECT_NULL_ERROR);
        }

        String notifyUser = null;
        if (null != productCategoryManagerVo) {
            List<String> users = new ArrayList<>();
            List<ManagerVo> costDirector = productCategoryManagerVo.getCostDirector();
            DemandProcDto demandProcDto = new DemandProcDto();
            if (null != costDirector && costDirector.size() > 0) {
                List<String> costDirectors = costDirector.stream().map(ManagerVo::getId).collect(Collectors.toList());
                users.addAll(costDirectors);
                demandProcDto.setCostDirector(costDirectors);
            }
            log.debug("costDirector:{}",costDirector);
            if (dto.getLectotypeType().equals(LectotypeTypeEnums.ONE.type)) {
                List<ManagerVo> materialAssistants = productCategoryManagerVo.getMaterialAssistants();
                log.debug("materialAssistants:{}",materialAssistants);
                if (null != materialAssistants && materialAssistants.size() > 0) {
                    log.debug("demandProcDto:{}",demandProcDto);
                    List<String> materialAssistantIds = materialAssistants.stream().map(ManagerVo::getId).collect(Collectors.toList());
                    users.addAll(materialAssistantIds);
                    users = users.stream().distinct().collect(Collectors.toList());

                    if (null == materialAssistants || materialAssistantIds.size() == 0) {
                        throw new BusinessException(ProductCategoryStatusCode.CATEGORY_NOT_CONFIG);
                    }


                }
            }
            notifyUser = String.join(",", users);
        } else {
            throw new BusinessException(ProductCategoryStatusCode.OBJECT_NULL_ERROR);
        }
        MsgLogDto msgLogDto = new MsgLogDto();
        msgLogDto.setMsgName("选型单新增：" + dto.getLectotypeName());
        msgLogDto.setMsgContent("名称：" + dto.getLectotypeName() + ",类型：" + LectotypeTypeEnums.lookFor(dto.getLectotypeType()).name);
        msgLogDto.setMsgType(3);
        msgLogDto.setNotifyType(1);
        msgLogDto.setNotifyUsers(notifyUser);
        msgLogDto.setLink("workbench/myWorkbench?tab=pendingProcessing");
        log.info("msgLogDto:{}",msgLogDto);
        RpcUtil.call(msgRpc.add(msgLogDto));

        DemandManagementPo demandManagementPo1 = new DemandManagementPo();
        demandManagementPo1.setDemandType(DemandTypeEnums.TWO.type);
        demandManagementPo1.setUpdateTime(DateTimeUtils.getCurrentTime());
        demandManagementPo1.setId(demandManagementPo.getId());
        demandMapper.updDemand(demandManagementPo1);
    }

    @Override
    public void updLectotype(DemandManagementLectotypeUpdDto dto) {
        List<String> fileIdList = dto.getFileIdList();
        if (fileIdList == null){
            fileIdList = new ArrayList<>();
        }
        dto.setFileIds(JSON.toJSONString(fileIdList));
        dto.setUpdateTime(DateTimeUtils.getCurrentTime());
        if(fileIdList.size()>0){
            documentService.batchAddFiles(fileIdList);
        }
        demandMapper.updLectotype(dto);
    }

    @Override
    public void updLectotypeStatus(DemandManagementLectotypeUpdDto dto) {
        demandMapper.updLectotypeStatus(dto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delLectotype(String lectotypeId) {
        //先删除物料
        List<String> list = demandMapper.getLectotypeMaterialByLtc(lectotypeId);
        if (list.size() > 0) {
            delLectotypeMaterial(lectotypeId, String.join(",", list));
        }
        demandMapper.delLectotype(lectotypeId);
        procurementDomainService.deleteByLectotypeId(lectotypeId);
        RpcUtil.call(demandProcessRpc.delLectotypeProcess(lectotypeId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delLectotypes(String lectotypeIds) {
        String[] lectotypeIdArr = lectotypeIds.split(",");
        for (String lectotypeId : lectotypeIdArr) {
            List<String> list = demandMapper.getLectotypeMaterialByLtc(lectotypeId);
            if (list.size() > 0) {
                delLectotypeMaterial(lectotypeId, String.join(",", list));
            }
            demandMapper.delLectotype(lectotypeId);
        }
        procurementDomainService.batchDeleteByLectotypeIds(Arrays.asList(lectotypeIdArr));
        for (String lectotypeId : lectotypeIdArr) {
            RpcUtil.call(demandProcessRpc.delLectotypeProcess(lectotypeId));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addLectotypeMaterial(LectotypeMaterialAddDto lectotypeMaterialAddDto) {
        MaterialAddDto addDto = lectotypeMaterialAddDto;
        //参数校验
        addDto.validate();
        //TODO 选型单状态是否符合
        DemandManagementLectotypeVo lectotypeById = demandMapper.getLectotypeById(lectotypeMaterialAddDto.getLectotypeId());
        //指定的不限制，招标的限制 非开标、上架中、已上架状态的选型单创建物料不能提交
        if (LectotypeTypeEnums.ONE.type.equals(lectotypeById.getLectotypeType()) && OperateEnums.SUBMIT.getId().equals(addDto.getOperate())) {
            if (!LectotypeStatusEnums.FIVE.type.equals(lectotypeById.getLectotypeStatus())
                    && !LectotypeStatusEnums.SIX.type.equals(lectotypeById.getLectotypeStatus())
                    && !LectotypeStatusEnums.SEVEN.type.equals(lectotypeById.getLectotypeStatus())) {
                //非开标状态的选型单创建物料不能提交
                log.error("Materials on the selection form that are not in the bid opening state cannot be submitted");
                throw new BusinessException(ProductCategoryStatusCode.SELECTION_FORM_MATERIAL_SHELF_LIMIT);
            }
        }
        String materialId = materialMaintenanceCommandService.addMaterial(addDto);
        demandMapper.addLectotypeMaterial(lectotypeMaterialAddDto.getLectotypeId(), materialId,DateTimeUtils.getCurrentTime());
        if(OperateEnums.SUBMIT.getId().equals(addDto.getOperate())){
            DemandManagementLectotypeUpdDto dto = new DemandManagementLectotypeUpdDto();
            dto.setLectotypeId(lectotypeById.getLectotypeId());
            dto.setLectotypeStatus(LectotypeStatusEnums.SIX.type);
            dto.setUpdateTime(DateTimeUtils.getCurrentTime());
            demandMapper.updLectotypeStatus(dto);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delLectotypeMaterial(String lectotypeId, String materialIds) {
        String[] materialIdArr = materialIds.split(",");
        List<String> materialIdList = Arrays.asList(materialIdArr);
        if (CollectionUtils.isNotEmpty(materialIdList)) {
            materialMaintenanceCommandService.batchDeleteMaterial(materialIdList);
            demandMapper.batchDelLectotypeMaterial(lectotypeId, materialIdList);
        }
    }

    @Override
    public List<IdNameBean> queryAllStatus() {
        return Arrays.stream(LectotypeStatusEnums.values())
                .map(e -> new IdNameBean(String.valueOf(e.type), e.name))
                .collect(Collectors.toList());
    }

    @Override
    public SelectionFormDetailsVo querySelectionFormById(String id) {
        SelectionFormDetailsVo detailsVo = new SelectionFormDetailsVo();
        DemandManagementLectotypeVo demandManagementLectotypeVo = demandMapper.getLectotypeById(id);
        if (demandManagementLectotypeVo == null) {
            return detailsVo;
        }
        UserVo userVoBaseResult2 = RpcUtil.call(systemRpc.getUserInfoById(demandManagementLectotypeVo.getCreateBy()));
        if(null!=userVoBaseResult2) {
            demandManagementLectotypeVo.setCreateByName(userVoBaseResult2.getName()+demandManagementLectotypeVo.getCreateBy());
        }
        UserVo userVoBaseResult = RpcUtil.call(systemRpc.getUserInfoById(demandManagementLectotypeVo.getUpdateBy()));
        if(null!=userVoBaseResult) {
            demandManagementLectotypeVo.setUpdateByName(userVoBaseResult.getName()+demandManagementLectotypeVo.getUpdateBy());
        }
        permissionCheck(demandManagementLectotypeVo.getDemandId());
        ProcurementQueryDto queryDto = new ProcurementQueryDto();
        queryDto.setLectotypeId(demandManagementLectotypeVo.getLectotypeId());
        queryDto.setLectotypeType(demandManagementLectotypeVo.getLectotypeType());
        ProcurementCostVo procurementCostVo = procurementDomainService.queryByModelSelection(queryDto);
        if (procurementCostVo!= null) {
            demandManagementLectotypeVo.setNegotiatedPrice(procurementCostVo.getNegotiatedPrice());
            demandManagementLectotypeVo.setDatumTargetPrice(procurementCostVo.getDatumTargetPrice());
            demandManagementLectotypeVo.setChallengeTargetPrice(procurementCostVo.getChallengeTargetPrice());
            demandManagementLectotypeVo.setOpenTenderPrice(procurementCostVo.getOpenTenderPrice());
            demandManagementLectotypeVo.setSetBidPrice(procurementCostVo.getSetBidPrice());
        }

        if(StringUtils.isNotBlank(demandManagementLectotypeVo.getFileIds())) {
            List<String> ids = (List<String>) JSON.parse(demandManagementLectotypeVo.getFileIds());
            List<FileInfoVo> documentInfoVos = documentService.queryByFileIds(ids);
            documentInfoVos.stream().forEach(fileInfoVo -> {fileInfoVo.setFileData(null);});
            detailsVo.setFileList(documentInfoVos);
        }else {
            detailsVo.setFileList(new ArrayList<>());
        }
        detailsVo.setDemandManagementLectotypeVo(demandManagementLectotypeVo);
        return detailsVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editLectotype(DemandManagementLectotypeDto dto) {
        DemandManagementLectotypeVo lectotypeById = demandMapper.getLectotypeById(dto.getLectotypeId());
        if (lectotypeById == null) {
            log.error("The lectotype:{} does not exist or has been deleted.",dto.getLectotypeId());
            throw new BusinessException(StatusCode.DATA_NOT_FOUND);
        }
        dto.setFileIds(JSON.toJSONString(dto.getFileIdList()));
        dto.setUpdateTime(DateTimeUtils.getCurrentTime());
        if(null!=dto.getFileIdList()&&dto.getFileIdList().size()>0){
            documentService.batchAddFiles(dto.getFileIdList());
        }
        DemandManagementLectotypeUpdDto updDto = new DemandManagementLectotypeUpdDto();
        BeanUtils.copyProperties(dto,updDto);
        demandMapper.updLectotype(updDto);

        try {
            DemandManagementPo demandManagementPo = demandMapper.selectDemandById(lectotypeById.getDemandId());
            permissionUtil.checkPermission(demandManagementPo.getProductCategoryId(), PermissionEnum.PROCUREMENT_EDIT);
            ProcurementCostEditDto editDto = build(dto);
            editDto.parameterVerification();
            procurementDomainService.edit(editDto);
        }catch (Exception e){
            log.info("editLectotype:不是成本总监，没有修改权限！");
        }
    }

    private ProcurementCostEditDto build(DemandManagementLectotypeDto dto) {
        ProcurementCostEditDto editDto = new ProcurementCostEditDto();
        editDto.setLectotypeId(dto.getLectotypeId());
        editDto.setLectotypeType(dto.getLectotypeType());
        editDto.setNegotiatedPrice(dto.getNegotiatedPrice());
        editDto.setChallengeTargetPrice(dto.getChallengeTargetPrice());
        editDto.setDatumTargetPrice(dto.getDatumTargetPrice());
        editDto.setOpenTenderPrice(dto.getOpenTenderPrice());
        editDto.setSetBidPrice(dto.getSetBidPrice());
        editDto.setBidOpeningTime(dto.getOpenBidTime());
        editDto.setBidIssuingTime(dto.getSendBidTime());
        return editDto;
    }

    public void delLectotypeOneMaterial(String lectotypeId, String materialId) {

        materialMaintenanceCommandService.deleteMaterial(materialId);
        demandMapper.delLectotypeMaterial(lectotypeId, materialId);

    }

    /* Started by AICoder, pid:j15288e969m134514398081f402bdf6bd5c9b55d */
    public void updLectotypeMaterialStatus(String materialId, Integer type) {
        DemandManagementLectotypeVo de = demandMapper.getLectotypeByMaterialId(materialId);
        if (de == null) {
            return;
        }

        // 如果选型单的状态是"已取消"那么就不再执行后续操作
        if (isLectotypeCancelled(de)) {
            return;
        }

        if (type == 1) {
            handleTypeOne(de, materialId);
        } else if (type == 2) {
            handleTypeTwo(de);
        }
    }

    private boolean isLectotypeCancelled(DemandManagementLectotypeVo de) {
        return LectotypeStatusEnums.TEN.type.equals(de.getLectotypeStatus());
    }

    // 处理 type == 1 的逻辑
    private void handleTypeOne(DemandManagementLectotypeVo de, String materialId) {
        MaterialDetailVo me = materialMaintenanceQueryService.queryMaterialDetailsById(materialId);
        if (me != null && me.getMaterial() != null &&
                me.getMaterial().getMaterialStatus().equals(MaterialStatusEnums.AVAILABLE_APPROVAL.getId())) {

            DemandManagementLectotypeUpdDto dto = new DemandManagementLectotypeUpdDto();
            dto.setLectotypeId(de.getLectotypeId());
            dto.setLectotypeStatus(LectotypeStatusEnums.SIX.type);
            dto.setUpdateTime(DateTimeUtils.getCurrentTime());
            demandMapper.updLectotypeStatus(dto);
        }
    }

    // 处理 type == 2 的逻辑
    private void handleTypeTwo(DemandManagementLectotypeVo de) {
        List<String> pageList = demandMapper.getLectotypeMaterialByLtc(de.getLectotypeId());

        for (String s : pageList) {
            MaterialDetailVo me = materialMaintenanceQueryService.queryMaterialDetailsById(s);
            if (me == null || me.getMaterial() == null ||
                    !me.getMaterial().getMaterialStatus().equals(MaterialStatusEnums.AVAILABLE.getId())) {
                return;
            }
        }

        DemandManagementLectotypeUpdDto dto = new DemandManagementLectotypeUpdDto();
        dto.setLectotypeId(de.getLectotypeId());
        dto.setLectotypeStatus(LectotypeStatusEnums.SEVEN.type);
        dto.setUpdateTime(DateTimeUtils.getCurrentTime());
        dto.setEndTime(DateTimeUtils.getCurrentTime());
        demandMapper.updLectotypeStatus(dto);

        List<DemandManagementLectotypeVo> list = demandMapper.pageLectotype(de.getDemandId());
        for (DemandManagementLectotypeVo vo : list) {
            if (!vo.getLectotypeStatus().equals(LectotypeStatusEnums.SEVEN.type)) {
                return;
            }
        }

        DemandManagementPo demandManagementPo1 = new DemandManagementPo();
        demandManagementPo1.setDemandType(DemandTypeEnums.THREE.type);
        demandManagementPo1.setUpdateTime(DateTimeUtils.getCurrentTime());
        demandManagementPo1.setId(de.getDemandId());
        demandManagementPo1.setActTimeComplet(DateTimeUtils.getCurrentTime());
        demandMapper.updDemand(demandManagementPo1);
    }

    /* Ended by AICoder, pid:j15288e969m134514398081f402bdf6bd5c9b55d */
    /**
     * 编辑权限查看权限归属一个角色
     * @param id 需求id
     */
    private void permissionCheck(String id) {
        DemandManagementPo demandManagementPo = demandMapper.selectDemandById(id);
        if(demandManagementPo!=null){
            permissionUtil.checkPermission(demandManagementPo.getProductCategoryId(), PermissionEnum.PROCUREMENT_EDIT);
        }
    }
    @Override
    public DemandManagementInnerVo getDemandManagement(DemandManagementInnerDto managementInnerDto) {
        DemandManagementInnerVo innerVo=new DemandManagementInnerVo();
        PageDemandDto pageDemandDto=new PageDemandDto();
        //pageDemandDto.setBillQuantityId(managementInnerDto.getBillQuantityId());
        pageDemandDto.setProjectId(managementInnerDto.getProjectId());
        pageDemandDto.setProductCategoryId(managementInnerDto.getProductCategoryId());
        List<DemandManagementVo> voList=demandMapper.pageDemand(pageDemandDto);
        log.info("getDemandManagement voList：{}",voList);
        if (CollectionUtils.isNotEmpty(voList)&&voList.size()==1){
            BeanUtils.copyProperties(voList.get(0),innerVo);
            log.info("copy result innerVo:{}",innerVo);
        }
        return innerVo;
    }


    /* Started by AICoder, pid:j80d7cad68w4b761494e08733003fe3eff97eff1 */
    @Override
    public List<DemandManagementInnerVo> getDemandManagementList(DemandManagementInnerDto managementInnerDto) {
        PageDemandDto pageDemandDto = new PageDemandDto();
        pageDemandDto.setProjectId(managementInnerDto.getProjectId());
        pageDemandDto.setProductCategoryId(managementInnerDto.getProductCategoryId());
        List<DemandManagementVo> voList = demandMapper.pageDemand(pageDemandDto);
        if (CollectionUtils.isEmpty(voList)) {
            return Collections.emptyList();
        }
        // 转换
        return voToInnerVolist(voList);
    }

    // 转换 DemandManagementVo 列表为 DemandManagementInnerVo 列表
    private List<DemandManagementInnerVo> voToInnerVolist(List<DemandManagementVo> voList) {
        List<DemandManagementInnerVo> innerVoList = new ArrayList<>();
        for (DemandManagementVo vo : voList) {
            DemandManagementInnerVo innerVo = new DemandManagementInnerVo();
            innerVo.setId(vo.getId());
            innerVo.setProjectId(vo.getProjectId());
            innerVo.setBillQuantityId(vo.getBillQuantityId());
            innerVo.setProductCategoryId(vo.getProductCategoryId());
            innerVo.setCreateUser(vo.getCreateUser());
            innerVo.setExpTimeComplet(vo.getExpTimeComplet());
            innerVo.setActTimeComplet(vo.getActTimeComplet());
            innerVo.setProcessor(vo.getProcessor());
            innerVo.setDemandType(vo.getDemandType());
            innerVo.setCreateBy(vo.getCreateBy());
            innerVo.setCreateTime(vo.getCreateTime());
            innerVo.setUpdateBy(vo.getUpdateBy());
            innerVo.setUpdateTime(vo.getUpdateTime());
            innerVo.setBillQuantitieTime(vo.getBillQuantitieTime());

            innerVoList.add(innerVo);
        }
        return innerVoList;
    }

    /* Ended by AICoder, pid:j80d7cad68w4b761494e08733003fe3eff97eff1 */

    @Override
    public void updateDemand(DemandManagementDto managementDto) {
        DemandManagementPo demandManagementPo=new DemandManagementPo();
        BeanUtils.copyProperties(managementDto,demandManagementPo);
        log.info("demandManagementPo:{}",demandManagementPo);
        demandMapper.updDemand(demandManagementPo);
    }

    @Override
    public void onceSubmit(String lectotypeId) {
        List<String> ids = demandMapper.getLectotypeMaterialByLtc(lectotypeId);
        log.info("materialIds ids:{}",JSON.toJSONString(ids));
        if(null==ids||ids.size()==0){
            return;
        }
        MaterialConditionQueryDto dto = new MaterialConditionQueryDto();
        DemandManagementPo demandManagementPo = demandMapper.getDemandManagementLectotypeById(lectotypeId);
        dto.setProductCategoryId(demandManagementPo.getProductCategoryId());
        dto.setOperation(OperateEnums.LISTING.getId());
        log.info("materialIds dto:{}",JSON.toJSONString(dto));
        List<MaterialVo> list=materialMaintenanceQueryService.queryMaterialByCondition(dto);
        if(null==list||list.size()==0){
            return;
        }
        List<String> materialIds = new ArrayList<>();
        List<String> materialIds2 = new ArrayList<>();
        List<String> materialAssistantIds = list.stream().map(MaterialVo::getId).collect(Collectors.toList());
        log.info("materialIds materialAssistantIds:{}",JSON.toJSONString(materialAssistantIds));
        for (String id : ids){
            if(materialAssistantIds.contains(id)){
                MaterialDetailVo materialDetailVo =  materialMaintenanceQueryService.queryMaterialDetailsById(id);
                if(null!=materialDetailVo&&StringUtils.isNotBlank(materialDetailVo.getMaterial().getPdmInfoId())){
                    materialIds2.add(id);
                }else {
                    materialIds.add(id);
                }

            }
        }
        log.info("materialIds:{}",JSON.toJSONString(materialIds));
        if(materialIds.size()>0){
            materialMaintenanceCommandService.batchHandlingMaterial(materialIds,OperateEnums.LISTING.getId(),1);

        }
        log.info("materialIds2:{}",JSON.toJSONString(materialIds2));
        if(materialIds2.size()>0){
            materialMaintenanceCommandService.batchHandlingMaterial(materialIds2,OperateEnums.LISTING.getId(),0);

        }

        if(materialIds.size()>0||materialIds2.size()>0){
            updLectotypeMaterialStatus(materialIds2.get(0),1);
        }


    }

    @Override
    public DemandAndLectotypeDetailsVo getDemandManagementLectotypeById(String lectotypeId){
        DemandAndLectotypeDetailsVo detailsVo = new DemandAndLectotypeDetailsVo();
        DemandManagementPo demandManagementPo = demandMapper.getDemandManagementLectotypeById(lectotypeId);
        if (demandManagementPo == null) {
            return null;
        }
        DemandManagementVo de = new DemandManagementVo();
        BeanUtils.copyProperties(demandManagementPo,de);
        ProductCategoryVo result = productCategoryQueryService.queryProductCategoryById(de.getProductCategoryId());
        if(null!=result){
            de.setProductCategoryName(result.getProductName());
        }
        ProjectDetailInfoVo projectDetailInfoVoBaseResult = RpcUtil.call(projectRpc.selectProjectInfo(de.getProjectId()));
        if(null!=projectDetailInfoVoBaseResult){
            de.setProjectName(projectDetailInfoVoBaseResult.getName());
        }
        UserVo userVoBaseResult = RpcUtil.call(systemRpc.getUserInfoById(de.getProcessor()));
        if(null!=userVoBaseResult) {
            de.setProcessorName(userVoBaseResult.getName()+de.getProcessor());
        }
        UserVo userVoBaseResult1 = RpcUtil.call(systemRpc.getUserInfoById(de.getCreateUser()));
        if(null!=userVoBaseResult1) {
            de.setCreateUser(userVoBaseResult1.getName()+de.getCreateUser());
        }
        detailsVo.setDemandManagementPo(de);

        SelectionFormDetailsVo selectionFormDetailsVo =  querySelectionFormByLectotypeId(lectotypeId);
        if (selectionFormDetailsVo == null) {
            return null;
        }
        detailsVo.setSelectionFormDetailsVo(selectionFormDetailsVo);
        return detailsVo;
    }
    /* Started by AICoder, pid:lfd2ekb2ba4196f142d90aeff01aa936f8b367bb */
    @Override
    public void updateDemandManagementInfoAndLecToTypeByCondition(DemandManagementInnerDto innerDto) {
        log.info("updateDemandManagementInfoAndLecToTypeByCondition innerDto:{}", JSON.toJSONString(innerDto));

        // 查询待更新的需求表数据
        List<DemandManagementVo> voList = demandMapper.queryDemandManagementByConditionIds(innerDto.getProjectId(), innerDto.getProductCategoryId());
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }

        String currentUserId = authService.getUserId();
        String currentDate = DateTimeUtils.getCurrentTime();

        voList.forEach(item -> {
            // 更新需求管理表
            DemandManagementPo demandManagementPo = new DemandManagementPo();
            demandManagementPo.setId(item.getId());
            // 取消状态
            demandManagementPo.setDemandType(DemandTypeEnums.TEN.type);
            demandManagementPo.setUpdateBy(currentUserId);
            demandManagementPo.setUpdateTime(currentDate);
            demandMapper.updDemand(demandManagementPo);

            // 更新需求选型表
            DemandManagementLectotypeUpdDto lectotypeUpdDto = new DemandManagementLectotypeUpdDto();
            lectotypeUpdDto.setDemandId(item.getId());
            // 取消状态
            lectotypeUpdDto.setLectotypeStatus(LectotypeStatusEnums.TEN.type);
            lectotypeUpdDto.setUpdateBy(currentUserId);
            lectotypeUpdDto.setUpdateTime(currentDate);
            demandMapper.updLectotypeByDemandId(lectotypeUpdDto);
        });
    }
    /* Ended by AICoder, pid:lfd2ekb2ba4196f142d90aeff01aa936f8b367bb */

    public SelectionFormDetailsVo querySelectionFormByLectotypeId(String id) {
        SelectionFormDetailsVo detailsVo = new SelectionFormDetailsVo();
        DemandManagementLectotypeVo demandManagementLectotypeVo = demandMapper.getLectotypeById(id);
        if (demandManagementLectotypeVo == null) {
            return detailsVo;
        }
        UserVo userVoBaseResult2 = RpcUtil.call(systemRpc.getUserInfoById(demandManagementLectotypeVo.getCreateBy()));
        if(null!=userVoBaseResult2) {
            demandManagementLectotypeVo.setCreateByName(userVoBaseResult2.getName()+demandManagementLectotypeVo.getCreateBy());
        }
        UserVo userVoBaseResult = RpcUtil.call(systemRpc.getUserInfoById(demandManagementLectotypeVo.getUpdateBy()));
        if(null!=userVoBaseResult) {
            demandManagementLectotypeVo.setUpdateByName(userVoBaseResult.getName()+demandManagementLectotypeVo.getUpdateBy());
        }
        ProcurementQueryDto queryDto = new ProcurementQueryDto();
        queryDto.setLectotypeId(demandManagementLectotypeVo.getLectotypeId());
        queryDto.setLectotypeType(demandManagementLectotypeVo.getLectotypeType());
        ProcurementCostVo procurementCostVo = procurementDomainService.queryByModelSelection(queryDto);
        if (procurementCostVo!= null) {
            demandManagementLectotypeVo.setNegotiatedPrice(procurementCostVo.getNegotiatedPrice());
            demandManagementLectotypeVo.setDatumTargetPrice(procurementCostVo.getDatumTargetPrice());
            demandManagementLectotypeVo.setChallengeTargetPrice(procurementCostVo.getChallengeTargetPrice());
            demandManagementLectotypeVo.setOpenTenderPrice(procurementCostVo.getOpenTenderPrice());
            demandManagementLectotypeVo.setSetBidPrice(procurementCostVo.getSetBidPrice());
        }

        if(StringUtils.isNotBlank(demandManagementLectotypeVo.getFileIds())) {
            List<String> ids = (List<String>) JSON.parse(demandManagementLectotypeVo.getFileIds());
            List<FileInfoVo> documentInfoVos = documentService.queryByFileIds(ids);
            documentInfoVos.stream().forEach(fileInfoVo -> {fileInfoVo.setFileData(null);});
            detailsVo.setFileList(documentInfoVos);
        }else {
            detailsVo.setFileList(new ArrayList<>());
        }
        detailsVo.setDemandManagementLectotypeVo(demandManagementLectotypeVo);
        return detailsVo;
    }

    /* Started by AICoder, pid:5d932j44d5m3528142650bf180d12a487c55b833 */
    @Override
    public BusinessLectotypeStatisticVo demandLectotypeStat(String productCategoryId, String time, Integer type) {
        /**
         * 根据产品类别ID、时间和类型统计需求标书。
         *
         * @param productCategoryId 产品类别ID
         * @param time              统计时间（格式：YYYY-MM-DD）
         * @param type              统计类型（例如：1-按天，2-按周，3-按月，4-按年）
         * @return 包含标书统计结果的 BusinessLectotypeStatisticVo 对象
         */
        BusinessLectotypeStatisticVo vo = null;
        switch (type) {
            case 1: // 天
                vo = demandMapper.demandLectotypeStatDay(productCategoryId, time);
                break;
            case 2: // 周
                DateTimeFormatter df1 = DateTimeFormatter.ofPattern("yyyyMMdd");
                LocalDate day = LocalDate.parse(time, df1);
                LocalDate monday = day.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
                LocalDate sunday = day.with(TemporalAdjusters.previousOrSame(DayOfWeek.SUNDAY));
                DateTimeFormatter df2 = DateTimeFormatter.ofPattern("yyyy-MM-dd 00:00:00");
                DateTimeFormatter df3 = DateTimeFormatter.ofPattern("yyyy-MM-dd 23:59:59");
                vo = demandMapper.demandLectotypeStatWeek(productCategoryId, df2.format(monday), df3.format(sunday));
                break;
            case 3: // 月
                vo = demandMapper.demandLectotypeStatMonth(productCategoryId, time);
                break;
            case 4: // 年
                vo = demandMapper.demandLectotypeStatYear(productCategoryId, time);
                break;
            default:
                break;
        }
        return vo;
    }

    @Override
    public BusinessLectotypeStatisticVo demandLectotypeStatAll() {
        /**
         * 统计所有需求标书。
         *
         * @return 包含标书统计结果的 BusinessLectotypeStatisticVo 对象
         */
        return demandMapper.demandLectotypeStatAll();
    }
    /* Ended by AICoder, pid:5d932j44d5m3528142650bf180d12a487c55b833 */
}
/* Ended by AICoder, pid:0b155947a9n99a314fb00a1f70528630a8b55413 */
