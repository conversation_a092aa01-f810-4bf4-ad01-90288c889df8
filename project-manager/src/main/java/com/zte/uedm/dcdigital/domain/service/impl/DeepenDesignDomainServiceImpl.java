package com.zte.uedm.dcdigital.domain.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zte.uedm.dcdigital.common.bean.PageVO;
import com.zte.uedm.dcdigital.common.bean.document.FileInfoVo;
import com.zte.uedm.dcdigital.common.bean.document.ProductUpgradeFileVo;
import com.zte.uedm.dcdigital.common.bean.product.ProductCategoryAndBrandInfoVo;
import com.zte.uedm.dcdigital.common.bean.product.ProductCategoryInfoVo;
import com.zte.uedm.dcdigital.common.bean.system.UserVo;
import com.zte.uedm.dcdigital.common.util.DateTimeUtils;
import com.zte.uedm.dcdigital.domain.aggregate.model.DeepenDesignFilesRelationEntity;
import com.zte.uedm.dcdigital.domain.aggregate.model.ItemInfoEntity;
import com.zte.uedm.dcdigital.domain.aggregate.repository.DeepenDesignFilesRelationRepository;
import com.zte.uedm.dcdigital.domain.aggregate.repository.ItemInfoRepository;
import com.zte.uedm.dcdigital.domain.common.valueobj.PageVoUpgrade;
import com.zte.uedm.dcdigital.domain.repository.BillOfQuantityRepository;
import com.zte.uedm.dcdigital.domain.service.DeepenDesignDomainService;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.ConstructionDrawingReviewMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.DeepenImplementationHistoryMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.mapper.ProjectDeepenImplementationMapper;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ConstructionDrawingReviewPo;
import com.zte.uedm.dcdigital.infrastructure.repository.po.DeepenImplementationHistoryPo;
import com.zte.uedm.dcdigital.infrastructure.repository.po.ProjectDeepenImplementationPo;
import com.zte.uedm.dcdigital.interfaces.web.dto.*;
import com.zte.uedm.dcdigital.interfaces.web.vo.*;
import com.zte.uedm.dcdigital.sdk.document.service.DocumentService;
import com.zte.uedm.dcdigital.sdk.product.service.ProductService;
import com.zte.uedm.dcdigital.sdk.system.service.AuthService;
import com.zte.uedm.dcdigital.sdk.system.service.SystemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 深化设计领域服务实现类
 */
@Service
@Slf4j
public class DeepenDesignDomainServiceImpl implements DeepenDesignDomainService {

    //在提资任务中完成上传附件的小类标识
    private static final String approvalResult = "1";

    @Autowired
    private ProjectDeepenImplementationMapper projectDeepenImplementationMapper;

    @Autowired
    private DeepenImplementationHistoryMapper deepenImplementationHistoryMapper;

    @Autowired
    private ConstructionDrawingReviewMapper constructionDrawingReviewMapper;

    @Autowired
    private AuthService authService;

    @Autowired
    private ItemInfoRepository itemInfoRepository;

    @Autowired
    private BillOfQuantityRepository billOfQuantityRepository;

    @Autowired
    private ProductService productService;

    @Autowired
    private SystemService systemService;

    @Autowired
    private DeepenDesignFilesRelationRepository designFilesRelationRepository;

    @Autowired
    private DocumentService documentService;

    @Override
    public DeepenImplementationVo queryDeepenImplementation(String itemId) {
        log.info("DeepenDesignDomainService.queryDeepenImplementation start, itemId: {}", itemId);
        
        ProjectDeepenImplementationPo po = projectDeepenImplementationMapper.selectByItemId(itemId);
        if (po == null) {
            log.info("No deepen implementation found for itemId: {}", itemId);
            return null;
        }

        DeepenImplementationVo vo = new DeepenImplementationVo();
        BeanUtils.copyProperties(po, vo);

        // 处理设计负责人字段，根据用户ID查询用户名
        if (StringUtils.isNotBlank(po.getDesignDirector())) {
            vo.setDesignDirector(getUserNameById(po.getDesignDirector()));
            vo.setDesignDirectorId(po.getDesignDirector());
        }

        // 处理深化设计状态，将状态码转换为前端需要的格式
        if (StringUtils.isNotBlank(po.getDeepenDesignStatus())) {
            DeepenImplementationVo.DeepenDesignStatusVo statusVo = new DeepenImplementationVo.DeepenDesignStatusVo();
            statusVo.setId(convertStatusToCode(po.getDeepenDesignStatus()));
            statusVo.setName(convertStatusToName(po.getDeepenDesignStatus()));
            vo.setDeepenDesignStatus(statusVo);
        }

        return vo;
    }

    @Override
    public PageVO<DeepenImplementationHistoryVo> queryImplementationHistory(DeepenImplementationHistoryQueryDto queryDto) {
        log.info("DeepenDesignDomainService.queryImplementationHistory start, queryDto: {}", queryDto);
        
        // 设置分页参数
        PageHelper.startPage(queryDto.getPageNum(), queryDto.getPageSize());
        
        List<DeepenImplementationHistoryPo> poList = deepenImplementationHistoryMapper.selectHistoryByItemId(queryDto);
        PageInfo<DeepenImplementationHistoryPo> pageInfo = new PageInfo<>(poList);
        
        List<DeepenImplementationHistoryVo> voList = poList.stream().map(po -> {
            DeepenImplementationHistoryVo vo = new DeepenImplementationHistoryVo();
            BeanUtils.copyProperties(po, vo);
            // 处理更新人字段，转换为"用户名+用户ID"格式
            if (StringUtils.isNotBlank(po.getUpdateBy())) {
                vo.setUpdateBy(getUserNameWithId(po.getUpdateBy()));
            }
            return vo;
        }).collect(Collectors.toList());
        
        PageVO<DeepenImplementationHistoryVo> result = new PageVO<>();
        result.setTotal((int) pageInfo.getTotal());
        result.setList(voList);
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editImplementation(DeepenImplementationEditDto editDto) {
        log.info("DeepenDesignDomainService.editImplementation start, editDto: {}", editDto);

        String currentTime = DateTimeUtils.getCurrentTime();
        String userId = authService.getUserId();


        // 查询现有记录
        ProjectDeepenImplementationPo existingPo = projectDeepenImplementationMapper.selectByItemId(editDto.getId());

        //如果登录id和设计负责人id不一致，抛出异常
        if (!userId.equals(existingPo.getDesignDirector())) {
            throw new RuntimeException("当前用户不是设计负责人，无权编辑");
        }

        ProjectDeepenImplementationPo po = new ProjectDeepenImplementationPo();
        BeanUtils.copyProperties(editDto, po);
        po.setDesignDirector(editDto.getDesignDirectorId());
        po.setUpdateBy(userId);
        po.setUpdateTime(currentTime);

        if (existingPo == null) {
            // 新增记录
            po.setCreateBy(userId);
            po.setCreateTime(currentTime);
            projectDeepenImplementationMapper.insertDeepenImplementation(po);
        } else {
            // 更新记录
            po.setCreateBy(existingPo.getCreateBy());
            po.setCreateTime(existingPo.getCreateTime());
            projectDeepenImplementationMapper.updateDeepenImplementationById(po);
        }

        // 记录历史
        DeepenImplementationHistoryPo historyPo = new DeepenImplementationHistoryPo();
        historyPo.setId(UUID.randomUUID().toString());
        historyPo.setItemId(editDto.getId());
        historyPo.setEngineeringDeepenProgress(editDto.getEngineeringDeepenProgress());
        historyPo.setDeepenDesignStatus(editDto.getDeepenDesignStatus());

        // 历史记录的创建人和创建时间逻辑：
        // 1. 如果是第一次创建深化实施记录，创建人和创建时间都是当前值
        // 2. 如果是更新已有记录，创建人和创建时间保持为主表的值，更新人和更新时间为当前值
        if (existingPo == null) {
            // 第一次创建，创建人和创建时间都是当前值
            historyPo.setCreateBy(userId);
            historyPo.setCreateTime(currentTime);
        } else {
            // 更新操作，创建人和创建时间保持为主表的值
            historyPo.setCreateBy(existingPo.getCreateBy());
            historyPo.setCreateTime(existingPo.getCreateTime());
        }
        historyPo.setUpdateBy(userId);
        historyPo.setUpdateTime(currentTime);

        deepenImplementationHistoryMapper.insertHistory(historyPo);

        log.info("DeepenDesignDomainService.editImplementation completed for itemId: {}", editDto.getId());
    }

    /**
     * 将状态转换为代码（A, B1, B2, B3, B4）
     */
    private String convertStatusToCode(String status) {
        if (StringUtils.isBlank(status)) {
            return "";
        }

        // 如果已经是代码格式，直接返回
        if (status.matches("^[AB][1-4]?$")) {
            return status;
        }

        return status;
    }

    /**
     * 将状态转换为名称（去掉前缀字母）
     */
    private String convertStatusToName(String status) {
        if (StringUtils.isBlank(status)) {
            return "";
        }

        // 根据代码返回对应的名称
        switch (status) {
            case "A":
                return "正常";
            case "B1":
                return "延误（客户需求变化）";
            case "B2":
                return "延误（产品原因）";
            case "B3":
                return "延误（商务原因）";
            case "B4":
                return "延误（其它）";
            default:
                // 如果是完整的状态描述，去掉前缀字母
                if (status.startsWith("A") || status.startsWith("B")) {
                    return status.substring(1);
                }
                return status;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addReviewRecord(ConstructionDrawingReviewAddDto addDto) {
        log.info("DeepenDesignDomainService.addReviewRecord start, addDto: {}", addDto);

        // 获取当前用户信息
        String currentUser = authService.getUserId();
        String currentTime = DateTimeUtils.getCurrentTime();
        log.info("currentUser+++++++++: {}", currentUser);

        // 构建PO对象
        ConstructionDrawingReviewPo po = new ConstructionDrawingReviewPo();
        po.setId(UUID.randomUUID().toString());
        po.setItemId(addDto.getItemId());
        po.setReviewTime(addDto.getReviewTime());
        po.setSummary(addDto.getSummary());
        po.setCreateBy(currentUser);
        po.setCreateTime(currentTime);
        po.setUpdateBy(currentUser);
        po.setUpdateTime(currentTime);

        // 插入数据库
        int result = constructionDrawingReviewMapper.insertReviewRecord(po);
        if (result <= 0) {
            throw new RuntimeException("新增施工图会审记录失败");
        }

        log.info("DeepenDesignDomainService.addReviewRecord success, id: {}", po.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editReviewRecord(ConstructionDrawingReviewEditDto editDto) {
        log.info("DeepenDesignDomainService.editReviewRecord start, editDto: {}", editDto);

        // 检查记录是否存在
        ConstructionDrawingReviewPo existingPo = constructionDrawingReviewMapper.selectReviewRecordById(editDto.getId());
        if (existingPo == null) {
            throw new RuntimeException("施工图会审记录不存在，ID: " + editDto.getId());
        }

        // 获取当前用户信息
        String currentUser = authService.getUserId();
        String currentTime = DateTimeUtils.getCurrentTime();

        // 构建更新的PO对象
        ConstructionDrawingReviewPo po = new ConstructionDrawingReviewPo();
        po.setId(editDto.getId());
        po.setReviewTime(editDto.getReviewTime());
        po.setSummary(editDto.getSummary());
        po.setUpdateBy(currentUser);
        po.setUpdateTime(currentTime);

        // 更新数据库
        int result = constructionDrawingReviewMapper.updateReviewRecordById(po);
        if (result <= 0) {
            throw new RuntimeException("编辑施工图会审记录失败");
        }

        log.info("DeepenDesignDomainService.editReviewRecord success, id: {}", editDto.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteReviewRecord(ConstructionDrawingReviewDeleteDto deleteDto) {
        log.info("DeepenDesignDomainService.deleteReviewRecord start, deleteDto: {}", deleteDto);

        // 检查记录是否存在
        ConstructionDrawingReviewPo existingPo = constructionDrawingReviewMapper.selectReviewRecordById(deleteDto.getId());
        if (existingPo == null) {
            throw new RuntimeException("施工图会审记录不存在，ID: " + deleteDto.getId());
        }

        // 删除记录
        int result = constructionDrawingReviewMapper.deleteReviewRecordById(deleteDto.getId());
        if (result <= 0) {
            throw new RuntimeException("删除施工图会审记录失败");
        }

        log.info("DeepenDesignDomainService.deleteReviewRecord success, id: {}", deleteDto.getId());
    }

    @Override
    public List<ConstructionDrawingReviewVo> queryAllRecord(String itemId) {
        log.info("DeepenDesignDomainService.queryAllRecord start, itemId: {}", itemId);

        // 查询项目下所有会审记录
        List<ConstructionDrawingReviewPo> poList = constructionDrawingReviewMapper.selectReviewRecordsByItemId(itemId);

        // 转换为VO对象
        List<ConstructionDrawingReviewVo> voList = poList.stream()
                .map(this::convertToVo)
                .collect(Collectors.toList());

        log.info("DeepenDesignDomainService.queryAllRecord success, count: {}", voList.size());
        return voList;
    }

    @Override
    public ConstructionDrawingReviewVo queryByRecordId(String id) {
        log.info("DeepenDesignDomainService.queryByRecordId start, id: {}", id);

        // 查询会审记录详情
        ConstructionDrawingReviewPo po = constructionDrawingReviewMapper.selectReviewRecordById(id);
        if (po == null) {
            log.warn("Construction drawing review record not found, id: {}", id);
            return null;
        }

        // 转换为VO对象
        ConstructionDrawingReviewVo vo = convertToVo(po);

        log.info("DeepenDesignDomainService.queryByRecordId success, id: {}", id);
        return vo;
    }

    @Override
    public PageVO<ProductCategorySimpleVo> queryCategoryByItemId(String itemId, Integer pageNo, Integer pageSize) {
        log.info("DeepenDesignDomainService.queryCategoryByItemId start, itemId: {}", itemId);

        // 1. 根据项目ID查询项目信息，获取商机ID
        ItemInfoEntity itemInfo = itemInfoRepository.getItemInfoDetailById(itemId);
        if (itemInfo == null) {
            log.warn("Project not found for itemId: {}", itemId);
            return new PageVO<>();
        }

        String projectId = itemInfo.getProjectId();
        log.info("Found projectId: {} for itemId: {}", projectId, itemId);

        // 2. 根据商机ID查询工程量清单，获取产品小类ID
        BillOfQuantityQueryDto queryDto = new BillOfQuantityQueryDto();
        queryDto.setId(projectId);

        List<BillOfQuantityVo> billOfQuantityList = billOfQuantityRepository.queryByCondition(queryDto);
        if (CollectionUtils.isEmpty(billOfQuantityList)) {
            log.info("No bill of quantity found for projectId: {}", projectId);
            return new PageVO<>();
        }

        // 3. 提取产品小类ID并去重
        Set<String> categoryIdSet = new HashSet<>();
        for (BillOfQuantityVo bill : billOfQuantityList) {
            if (StringUtils.isNotBlank(bill.getProductSubcategory())) {
                categoryIdSet.add(bill.getProductSubcategory());
            }
        }

        if (categoryIdSet.isEmpty()) {
            log.info("No product categories found for projectId: {}", projectId);
            return new PageVO<>();
        }

        // 4. 调用ProductService获取产品小类信息（包含pathname）
        List<String> categoryIds = new ArrayList<>(categoryIdSet);
        List<ProductCategoryAndBrandInfoVo> productCategoryInfoList = productService.selectProductCategoryAndBrandList(categoryIds);

        if (CollectionUtils.isEmpty(productCategoryInfoList)) {
            log.warn("No product category info found for categoryIds: {}", categoryIds);
            return new PageVO<>();
        }

        // 5. 转换为ProductCategorySimpleVo
        List<ProductCategorySimpleVo> result = new ArrayList<>();
        for (ProductCategoryAndBrandInfoVo categoryInfo : productCategoryInfoList) {
            ProductCategorySimpleVo simpleVo = new ProductCategorySimpleVo();
            simpleVo.setId(categoryInfo.getId());
            simpleVo.setPathName(categoryInfo.getPathName());
            result.add(simpleVo);
        }

        log.info("DeepenDesignDomainService.queryCategoryByItemId completed, found {} categories for itemId: {}",
                result.size(), itemId);

        //根据pageNo和pageSize进行分页
        int fromIndex = (pageNo - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, result.size());
        List<ProductCategorySimpleVo> pagedResult = result.subList(fromIndex, toIndex);
        PageVO<ProductCategorySimpleVo> pagevo = new PageVO<>(result.size(), pagedResult);

        return pagevo;
    }

    /**
     * 将PO对象转换为VO对象
     *
     * @param po PO对象
     * @return VO对象
     */
    private ConstructionDrawingReviewVo convertToVo(ConstructionDrawingReviewPo po) {
        ConstructionDrawingReviewVo vo = new ConstructionDrawingReviewVo();
        vo.setId(po.getId());
        vo.setReviewTime(po.getReviewTime());
        vo.setSummary(po.getSummary());

        // 处理创建人字段，转换为"用户名+用户ID"格式
        if (StringUtils.isNotBlank(po.getCreateBy())) {
            vo.setCreateBy(getUserNameWithId(po.getCreateBy()));
        }
        vo.setCreateTime(po.getCreateTime());

        // 处理更新人字段，转换为"用户名+用户ID"格式
        if (StringUtils.isNotBlank(po.getUpdateBy())) {
            vo.setUpdateBy(getUserNameWithId(po.getUpdateBy()));
        }
        vo.setUpdateTime(po.getUpdateTime());
        return vo;
    }

    /**
     * 根据用户ID获取"用户名+用户ID"格式的字符串
     *
     * @param userId 用户ID
     * @return 用户名+用户ID格式的字符串，如果查询失败则返回原用户ID
     */
    private String getUserNameWithId(String userId) {
        if (StringUtils.isBlank(userId)) {
            return userId;
        }

        try {
            UserVo userVo = systemService.getUserinfoById(userId);
            if (userVo != null && StringUtils.isNotBlank(userVo.getName())) {
                // 返回"用户名+用户ID"格式
                return userVo.getName() + userId;
            }
        } catch (Exception e) {
            log.warn("Failed to query user info for userId: {}, error: {}", userId, e.getMessage());
        }

        // 如果查询失败，返回原用户ID
        return userId;
    }

    /**
     * 根据用户ID获取用户名
     *
     * @param userId 用户ID
     * @return 用户名，如果查询失败则返回原用户ID
     */
    private String getUserNameById(String userId) {
        if (StringUtils.isBlank(userId)) {
            return userId;
        }

        try {
            UserVo userVo = systemService.getUserinfoById(userId);
            if (userVo != null && StringUtils.isNotBlank(userVo.getName())) {
                // 只返回用户名
                return userVo.getName();
            }
        } catch (Exception e) {
            log.warn("Failed to query user info for userId: {}, error: {}", userId, e.getMessage());
        }

        // 如果查询失败，返回原用户ID
        return userId;
    }

    @Override
    public DeepenDesignOverviewVo queryOverview(String id) {
        log.info("DeepenDesignDomainService.queryOverview start, id: {}", id);

        // 查询深化实施信息
        ProjectDeepenImplementationPo po = projectDeepenImplementationMapper.selectByItemId(id);
        if (po == null) {
            log.info("No deepen implementation found for id: {}", id);
            return null;
        }

        // 创建概览VO对象
        DeepenDesignOverviewVo overviewVo = new DeepenDesignOverviewVo();

        // 设置工程深化状态
        if (StringUtils.isNotBlank(po.getDeepenDesignStatus())) {
            overviewVo.setDeepenDesignStatus(po.getDeepenDesignStatus());
        }

        // 设置工程深化进度
        overviewVo.setEngineeringDeepenProgress(po.getEngineeringDeepenProgress());

        // 设置各个时间节点
        overviewVo.setStartTime(po.getStartTime());
        overviewVo.setRequestInformationCompletionTime(po.getRequestInformationCompletionTime());
        overviewVo.setActualInformationCompletionTime(po.getActualInformationCompletionTime());
        overviewVo.setFirstBatchMaterialPreparationOutputTime(po.getFirstBatchMaterialPreparationOutputTime());
        overviewVo.setLongPeriodMaterialListOutputTime(po.getLongPeriodMaterialListOutputTime());
        overviewVo.setOverallMaterialPreparationLockTime(po.getOverallMaterialPreparationLockTime());

        log.info("DeepenDesignDomainService.queryOverview success, id: {}", id);
        return overviewVo;
    }

    @Override
    public DeepenImplementationVo innerQueryDeepenImplementation(String itemId) {
        ProjectDeepenImplementationPo po = projectDeepenImplementationMapper.selectByItemId(itemId);
        if (po == null) {
            log.info("No deepen implementation found for itemId: {}", itemId);
            return null;
        }
        DeepenImplementationVo vo = new DeepenImplementationVo();
        BeanUtils.copyProperties(po, vo);
        return vo;
    }

    /* Started by AICoder, pid:251b7p43a8pacd8147e3093c100b80521e550da4 */
    @Override
    public PageVoUpgrade<ProductUpgradeVo> querySubmissionInfoByItemId(ProductUpgradeQueryDto queryDto) {
        // 使用PageHelper分页
        int pageNum = queryDto.getPageNum();
        int pageSize = queryDto.getPageSize();
        Page<DeepenDesignFilesRelationEntity> page = PageHelper.startPage(pageNum, pageSize);
        //对去重后的数据(根据项目id+产品小类id)进行分页
        List<DeepenDesignFilesRelationEntity> relationEntityList = designFilesRelationRepository.selectDeepenGroupByItemIdAndCategoryFilesRelationList(queryDto.getItemId());
        if (CollectionUtils.isEmpty(relationEntityList)) {
            return new PageVoUpgrade<>();
        }
        // 产品小类信息
        List<String> categoryIds = relationEntityList.stream()
                .map(DeepenDesignFilesRelationEntity::getProductCategoryId)
                .collect(Collectors.toList());
        List<ProductCategoryInfoVo> categoryInfoVos = productService.selectProductCategoryList(categoryIds);
        Map<String, String> categoryMap = categoryInfoVos.stream()
                .collect(Collectors.toMap(ProductCategoryInfoVo::getId, ProductCategoryInfoVo::getPathName));

        // 提交人信息
        List<String> submitUserIds = relationEntityList.stream()
                .map(DeepenDesignFilesRelationEntity::getSubmitUser)
                .collect(Collectors.toList());
        List<UserVo> userinfoByIds = systemService.getUserinfoByIds(submitUserIds);
        Map<String, String> userMap = userinfoByIds.stream()
                .collect(Collectors.toMap(UserVo::getId, UserVo::getDisplayText));
        //完整流程关联文件数据
        List<DeepenDesignFilesRelationEntity> relationAllList = designFilesRelationRepository.selectDeepenDesignFilesRelationListByItemId(queryDto.getItemId());
        //将relationAllList数据中的同项目id、产品小类id的数据归为同一组,其中key为最新关联的flowId(createTime最新的flowId),value为同组的List<String>flowId集合
        // 调用方法获取分组结果
        Map<String, List<String>> resultMap = groupByProjectAndProductCategory(relationAllList);
        List<ProductUpgradeVo> upgradeVos = new ArrayList<>();
        for (DeepenDesignFilesRelationEntity item : relationEntityList) {
            ProductUpgradeVo upgradeVo = new ProductUpgradeVo();
            upgradeVo.setCategoryId(item.getProductCategoryId());
            upgradeVo.setCategoryPathName(categoryMap.getOrDefault(item.getProductCategoryId(), ""));
            //根据当前flowId对应的flowId列表,获取对应的附件信息
            List<ProductUpgradeFileVo> upgradeFileVos = documentService.queryProductUpgradeFilesByFlowIds(resultMap.getOrDefault(item.getFlowId(), Collections.emptyList()));
            List<FileInfoVo> attachments = upgradeFileVos.stream()
                    .filter(vo -> vo.getFileInfos() != null)
                    .flatMap(vo -> vo.getFileInfos().stream())
                    .collect(Collectors.toList());
            upgradeVo.setAttachments(attachments);
            upgradeVo.setSubmitter(userMap.getOrDefault(item.getSubmitUser(), ""));
            upgradeVo.setUpdateTime(item.getUpdateTime());
            upgradeVos.add(upgradeVo);
        }

        PageVoUpgrade<ProductUpgradeVo> pageVO = new PageVoUpgrade<>(page.getTotal(), upgradeVos);
        // 提资任务完成率
        pageVO.setCompletionRate(calculateCompletionRate(queryDto.getItemId()));
        return pageVO;
    }

    /* Ended by AICoder, pid:251b7p43a8pacd8147e3093c100b80521e550da4 */

    @Override
    public int addDeepenDesignFilesRelation(DeepenDesignFilesRelationEntity deepenDesignFilesRelationEntity) {
        return designFilesRelationRepository.addDeepenDesignFilesRelation(deepenDesignFilesRelationEntity);
    }

    @Override
    public int updateDeepenDesignFilesRelation(DeepenDesignFilesRelationEntity deepenDesignFilesRelationEntity) {
        return designFilesRelationRepository.updateDeepenDesignFilesRelation(deepenDesignFilesRelationEntity);
    }

    @Override
    public List<DeepenDesignFilesRelationEntity> queryDeepenDesignFilesRelation(DeepenDesignFilesRelationEntity deepenDesignFilesRelationEntity) {
        return designFilesRelationRepository.queryDeepenDesignFilesRelation(deepenDesignFilesRelationEntity);
    }

    /* Started by AICoder, pid:fe305a81b2299d714a0f0aee904e442f08601dc3 */
    @Override
    public List<DeepenImplementationVo> queryInnerDeepenImplementationList(List<String> itemIds) {
        // 从数据库获取PO列表
        List<ProjectDeepenImplementationPo> poList = projectDeepenImplementationMapper.selectDeepenImplementationListByItemIds(itemIds);

        // 处理空结果集
        if (CollectionUtils.isEmpty(poList)) {
            log.info("No deepen implementation found for itemIds: {}", itemIds);
            return Collections.emptyList();
        }

        // 使用流式处理转换PO到VO
        return poList.stream()
                .map(po -> {
                    DeepenImplementationVo vo = new DeepenImplementationVo();
                    BeanUtils.copyProperties(po, vo);
                    return vo;
                })
                .collect(Collectors.toList());
    }

    /* Ended by AICoder, pid:fe305a81b2299d714a0f0aee904e442f08601dc3 */

    /* Started by AICoder, pid:fe04ai519bx5bb71406a0837e055e329dd65d188 */
    /**
     * 计算完成率（四舍五入保留整数）
     *
     * @param itemId 项目唯一标识
     * @return 完成率百分比值（整数）
     */
    private int calculateCompletionRate(String itemId) {
        List<DeepenDesignFilesRelationEntity> relationEntityList = designFilesRelationRepository.selectDeepenDesignFilesRelationListByItemId(itemId);
        //将itemId、productCategoryId相同的数据收集为map
        Map<String, List<DeepenDesignFilesRelationEntity>> stringListMap =
                relationEntityList.stream().collect(Collectors.groupingBy(entity ->
                        entity.getItemId() + "-" + entity.getProductCategoryId()));
        // 提资任务中总小类数量
        long totalCount = stringListMap.size();

        // 提资任务中完成了附件上传的小类数量
        long passCount = stringListMap.values().stream()
                .filter(list -> list.stream().anyMatch(entity -> approvalResult.equals(entity.getApprovalResult())))
                .count();
        if (totalCount == 0) return 0; // 防止除以0的情况

        // 计算完成率并乘以100，然后四舍五入保留最接近的整数
        BigDecimal completionRate = new BigDecimal(passCount)
                .divide(new BigDecimal(totalCount), 4, RoundingMode.HALF_UP)
                .multiply(new BigDecimal(100))
                .setScale(0, RoundingMode.HALF_UP); // 四舍五入到整数

        // 转换为int类型返回
        return completionRate.intValue();
    }
    /* Ended by AICoder, pid:fe04ai519bx5bb71406a0837e055e329dd65d188 */

    private static Map<String, List<String>> groupByProjectAndProductCategory(List<DeepenDesignFilesRelationEntity> entities) {
        // 使用TreeMap来存储分组结果，保证输出有序(可选)
        Map<String, TreeMap<LocalDateTime, String>> tempMap = new HashMap<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        for (DeepenDesignFilesRelationEntity entity : entities) {
            String key = entity.getItemId() + "-" + entity.getProductCategoryId();
            LocalDateTime createTime = LocalDateTime.parse(entity.getCreateTime(), formatter);

            tempMap.putIfAbsent(key, new TreeMap<>());
            tempMap.get(key).put(createTime, entity.getFlowId());
        }

        // 构建最终结果map
        Map<String, List<String>> resultMap = new HashMap<>();
        for (Map.Entry<String, TreeMap<LocalDateTime, String>> entry : tempMap.entrySet()) {
            TreeMap<LocalDateTime, String> timeFlowIdMap = entry.getValue();
            String latestFlowId = timeFlowIdMap.lastEntry().getValue();
            List<String> flowIdList = new ArrayList<>(timeFlowIdMap.values());

            resultMap.put(latestFlowId, flowIdList);
        }

        return resultMap;
    }
}
